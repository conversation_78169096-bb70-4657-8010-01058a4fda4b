import React, { useState, useEffect, useRef } from "react";
import {
  HiXMark,
  HiChevronLeft,
  HiChevronRight,
  HiUser,
} from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { getConfidenceColor } from "../../utils/colorUtils";
import { usePicks } from "../../contexts/PicksContext";

interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[];
}

interface PickDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  pick: Pick | null;
}

interface ResearchItem {
  id: number;
  title: string;
  content: string;
}

const PickDetailModal: React.FC<PickDetailModalProps> = ({
  isOpen,
  onClose,
  pick,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentResearchIndex, setCurrentResearchIndex] = useState(0);
  const modalRef = useRef<HTMLDivElement>(null);
  const { addPick, isPickSelected } = usePicks();

  // Mock research data - replace with real data later
  const researchItems: ResearchItem[] = [
    {
      id: 1,
      title: "Capper Research 1",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 2,
      title: "Capper Research 2",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 3,
      title: "Capper Research 3",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 4,
      title: "Capper Research 4",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 5,
      title: "Capper Research 5",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 6,
      title: "Capper Research 6",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 7,
      title: "Capper Research 7",
      content: "Placeholder text for handicapper research",
    },
  ];

  // Handle modal open/close animations
  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
      const timer = setTimeout(() => setIsAnimating(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen, onClose]);

  // Handle escape key to close
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [isOpen, onClose]);

  // Research navigation functions
  const goToPreviousResearch = () => {
    setCurrentResearchIndex((prev) =>
      prev === 0 ? researchItems.length - 1 : prev - 1
    );
  };

  const goToNextResearch = () => {
    setCurrentResearchIndex((prev) =>
      prev === researchItems.length - 1 ? 0 : prev + 1
    );
  };

  // Handle add to list
  const handleAddToList = () => {
    if (!pick) return;

    const isSelected = isPickSelected("pick", pick.id);
    if (!isSelected) {
      addPick({
        sourceType: "pick",
        sourceId: pick.id,
        playerName: pick.playerName || "Unknown Player",
        playerNumber: pick.playerNumber || "?",
        betType: pick.betType || "Standard Bet",
        gameInfo: pick.gameInfo || "Game info unavailable",
        confidence: pick.confidence || 50,
      });
    }
  };

  if (!isAnimating && !isOpen) return null;
  if (!pick) return null;

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-300 ease-in-out ${
        isOpen
          ? "bg-black bg-opacity-75 backdrop-blur-sm"
          : "bg-transparent pointer-events-none"
      }`}
    >
      <div
        ref={modalRef}
        className={`bg-[#233e6c] rounded-xl shadow-2xl w-full max-w-7xl max-h-[95vh] overflow-hidden transition-all duration-300 ease-in-out transform ${
          isOpen
            ? "scale-100 opacity-100 translate-y-0"
            : "scale-95 opacity-0 translate-y-4"
        }`}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center transition-colors duration-200"
        >
          <HiXMark className="w-5 h-5 text-white" />
        </button>

        <div className="p-4 lg:p-6 overflow-y-auto max-h-[95vh]">
          {/* Top Section - 2x3 Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 lg:grid-rows-2 gap-4 lg:gap-6 mb-6 lg:h-[400px]">
            {/* Row 1, Column 1 - Jersey Icon + Player Info */}
            <div className="flex items-start gap-4 lg:pr-4">
              {/* Jersey Icon - Positioned to match mockup */}
              <div
                className="w-24 h-24 lg:w-28 lg:h-28 rounded-full flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden flex-shrink-0"
                style={{
                  border: `3px solid ${getConfidenceColor(pick.confidence)}`,
                }}
              >
                <IoShirtOutline
                  className="w-16 h-16 lg:w-20 lg:h-20 absolute"
                  style={{ color: getConfidenceColor(pick.confidence) }}
                />
                <div className="text-white font-bold text-lg lg:text-xl z-10 relative">
                  {pick.playerNumber || "?"}
                </div>
              </div>

              {/* Player Information Text */}
              <div className="flex flex-col justify-start flex-grow min-w-0">
                <h2 className="text-xl lg:text-2xl font-bold text-white mb-1 lg:mb-2 leading-tight">
                  {pick.playerName || "Unknown Player"}
                </h2>
                <p className="text-base lg:text-lg font-bold text-white mb-1 lg:mb-2 leading-tight">
                  {pick.betType || "Standard Bet"}
                </p>
                <p className="text-xs lg:text-sm text-gray-300 leading-tight">
                  {pick.gameInfo || "Game info unavailable"}
                </p>
              </div>
            </div>

            {/* Row 1, Column 2 - Chart Section */}
            <div className="bg-[#1a2d54] rounded-lg p-4 lg:p-6 flex items-center justify-center min-h-[160px] lg:min-h-[180px]">
              <div className="text-center">
                <div className="text-lg lg:text-2xl font-bold text-white mb-2">
                  Past 10 Days Chart
                </div>
                <div className="text-sm lg:text-base text-gray-400">
                  Chart visualization will go here
                </div>
              </div>
            </div>

            {/* Row 1 & 2, Column 3 - Confidence Rating (spans both rows) */}
            <div className="md:col-span-2 lg:col-span-1 lg:row-span-2 flex flex-col items-center justify-center bg-[#1a2d54] rounded-lg p-4 min-h-[160px] lg:min-h-full">
              <div
                className="text-5xl md:text-6xl lg:text-8xl font-bold mb-2"
                style={{ color: getConfidenceColor(pick.confidence || 50) }}
              >
                {pick.confidence || 50}
              </div>
              <div className="text-base md:text-lg lg:text-xl font-bold text-white text-center">
                Confidence
                <br />
                Rating
              </div>
            </div>

            {/* Row 2, Column 1 - Game Logs Section */}
            <div className="bg-[#1a2d54] rounded-lg p-4 lg:p-6 flex items-center justify-center min-h-[160px] lg:min-h-[180px]">
              <div className="text-center">
                <div className="text-lg lg:text-2xl font-bold text-white mb-2">
                  Game
                  <br />
                  Logs
                </div>
              </div>
            </div>

            {/* Row 2, Column 2 - Picked By Section */}
            <div className="flex flex-col items-center justify-center">
              <h3 className="text-lg lg:text-2xl font-bold text-white mb-3 lg:mb-4 text-center">
                Picked By
              </h3>
              <div className="grid grid-cols-3 gap-2 mb-2 justify-center justify-items-center content-start w-[120px] lg:w-[136px] h-[120px] lg:h-[136px] mx-auto">
                {Array.from({ length: Math.min(pick.expertCount || 0, 9) }).map(
                  (_, index) => (
                    <div
                      key={index}
                      className="w-8 h-8 lg:w-10 lg:h-10 rounded-full border-2 border-gray-600 bg-gray-700 flex items-center justify-center"
                    >
                      <HiUser className="w-4 h-4 lg:w-6 lg:h-6 text-gray-400" />
                    </div>
                  )
                )}
              </div>
              {(pick.additionalExperts || 0) > 0 && (
                <p className="text-xs lg:text-sm text-gray-400 text-center">
                  and {pick.additionalExperts} more
                </p>
              )}
            </div>
          </div>

          {/* Bottom Section - Research and Add to Cart */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6">
            {/* Research Gallery - Takes up 3 columns */}
            <div className="lg:col-span-3">
              <h3 className="text-xl lg:text-2xl font-bold text-white mb-4">
                Research
              </h3>
              <div className="relative">
                {/* Navigation Arrows */}
                <button
                  onClick={goToPreviousResearch}
                  className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 w-8 h-8 lg:w-10 lg:h-10 bg-[#233e6c] border-2 border-white rounded-full flex items-center justify-center hover:bg-[#1a2d54] transition-colors duration-200"
                >
                  <HiChevronLeft className="w-4 h-4 lg:w-6 lg:h-6 text-white" />
                </button>
                <button
                  onClick={goToNextResearch}
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 w-8 h-8 lg:w-10 lg:h-10 bg-[#233e6c] border-2 border-white rounded-full flex items-center justify-center hover:bg-[#1a2d54] transition-colors duration-200"
                >
                  <HiChevronRight className="w-4 h-4 lg:w-6 lg:h-6 text-white" />
                </button>

                {/* Research Gallery Container - Show 3 items at a time to match mockup */}
                <div className="overflow-hidden mx-10 lg:mx-12">
                  <div
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{
                      transform: `translateX(-${
                        currentResearchIndex * 33.333
                      }%)`,
                    }}
                  >
                    {researchItems.map((item, index) => (
                      <div key={item.id} className="flex-shrink-0 w-1/3 px-2">
                        <div className="bg-[#1a2d54] rounded-lg p-3 lg:p-4 h-24 lg:h-32 flex items-center justify-center">
                          <div className="text-center">
                            <div className="text-xs lg:text-sm font-bold text-white mb-1 lg:mb-2">
                              {item.title}
                            </div>
                            <div className="text-xs text-gray-400">
                              {item.content}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Add to Cart Button - Takes up 1 column */}
            <div className="lg:col-span-1 flex items-end justify-center lg:justify-end">
              <button
                onClick={handleAddToList}
                className={`shadow-md hover:shadow-lg transition-all duration-300 ease-linear text-base lg:text-lg font-bold px-4 lg:px-6 py-2 lg:py-3 rounded-lg whitespace-nowrap ${
                  isPickSelected("pick", pick.id)
                    ? "bg-green-500 text-white cursor-default"
                    : "bg-white hover:bg-gray-300 hover:cursor-pointer text-[#061844]"
                }`}
              >
                {isPickSelected("pick", pick.id) ? "Added ✓" : "Add to Cart"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PickDetailModal;
